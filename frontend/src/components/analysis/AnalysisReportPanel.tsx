import React from 'react';
import ReactMarkdown from 'react-markdown';

interface AnalysisReport {
  analysis_text: string;
}

interface AnalysisReportPanelProps {
  report: AnalysisReport | null;
  loading: boolean;
  error: string | null;
}

const AnalysisReportPanel: React.FC<AnalysisReportPanelProps> = ({ report, loading, error }) => {
  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <span className="text-gray-500">分析报告加载中...</span>
      </div>
    );
  }
  if (error) {
    return (
      <div className="text-red-500 py-4">分析报告加载失败：{error}</div>
    );
  }
  if (!report) {
    return (
      <div className="text-gray-400 py-4">暂无分析报告数据</div>
    );
  }

  return (
    <div className="space-y-6 p-4 border rounded bg-white shadow">
      <div className="prose prose-sm max-w-none">
        <ReactMarkdown
          components={{
            h1: ({ children }) => (
              <h1 className="text-2xl font-bold mb-4 mt-6 first:mt-0">{children}</h1>
            ),
            h2: ({ children }) => (
              <h2 className="text-xl font-bold mb-3 mt-5 first:mt-0">{children}</h2>
            ),
            h3: ({ children }) => (
              <h3 className="text-lg font-bold mb-2 mt-4 first:mt-0">{children}</h3>
            ),
            h4: ({ children }) => (
              <h4 className="text-base font-bold mb-2 mt-3 first:mt-0">{children}</h4>
            ),
            h5: ({ children }) => (
              <h5 className="text-sm font-bold mb-1 mt-2 first:mt-0">{children}</h5>
            ),
            h6: ({ children }) => (
              <h6 className="text-xs font-bold mb-1 mt-2 first:mt-0">{children}</h6>
            ),
            ul: ({ children }) => (
              <ul className="list-disc list-inside ml-4 space-y-1 my-3">{children}</ul>
            ),
            ol: ({ children }) => (
              <ol className="list-decimal list-inside ml-4 space-y-1 my-3">{children}</ol>
            ),
            li: ({ children }) => (
              <li className="ml-2">{children}</li>
            ),
          }}
        >
          {report.analysis_text}
        </ReactMarkdown>
      </div>
    </div>
  );
};

export default AnalysisReportPanel;